// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:smartfarming_bapeltan/common/app_colors.dart';

class SchedulingScreen extends StatefulWidget {
  const SchedulingScreen({Key? key}) : super(key: key);

  @override
  State<SchedulingScreen> createState() => _SchedulingScreenState();
}

class _SchedulingScreenState extends State<SchedulingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // Header Section
          _buildHeaderSection(),
          
          SizedBox(height: 24),
          
          // Quick Schedule Section
          _buildQuickScheduleSection(),
          
          SizedBox(height: 24),
          
          // Active Schedules Section
          _buildActiveSchedulesSection(),
          
          SizedBox(height: 24),
          
          // Automation Rules Section
          _buildAutomationRulesSection(),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColor.hijau2,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: Colors.white,
                size: 28,
              ),
              SizedBox(width: 12),
              Text(
                "Scheduling & Automation",
                style: TextStyle(
                  fontSize: 22,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            "Manage your smart farming schedules and automation rules",
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickScheduleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Quick Schedule",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColor.hijau1,
          ),
        ),
        SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickScheduleCard(
                icon: Icons.water_drop,
                title: "Watering",
                subtitle: "Schedule irrigation",
                onTap: () {
                  // TODO: Navigate to watering schedule
                },
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildQuickScheduleCard(
                icon: Icons.air,
                title: "Ventilation",
                subtitle: "Schedule fans",
                onTap: () {
                  // TODO: Navigate to ventilation schedule
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickScheduleCard(
                icon: Icons.wb_sunny,
                title: "Lighting",
                subtitle: "Schedule lights",
                onTap: () {
                  // TODO: Navigate to lighting schedule
                },
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildQuickScheduleCard(
                icon: Icons.thermostat,
                title: "Climate",
                subtitle: "Temperature control",
                onTap: () {
                  // TODO: Navigate to climate schedule
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickScheduleCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 2,
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColor.hijau3,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppColor.hijau1,
                size: 24,
              ),
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColor.hijau1,
              ),
            ),
            SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveSchedulesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Active Schedules",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColor.hijau1,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to all schedules
              },
              child: Text(
                "View All",
                style: TextStyle(
                  color: AppColor.hijau1,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 12),
        _buildScheduleCard(
          title: "Morning Watering",
          time: "06:00 AM",
          device: "Sprinkler System",
          isActive: true,
        ),
        SizedBox(height: 8),
        _buildScheduleCard(
          title: "Evening Ventilation",
          time: "06:00 PM",
          device: "Exhaust Fans",
          isActive: true,
        ),
        SizedBox(height: 8),
        _buildScheduleCard(
          title: "Night Lighting",
          time: "08:00 PM",
          device: "LED Grow Lights",
          isActive: false,
        ),
      ],
    );
  }

  Widget _buildScheduleCard({
    required String title,
    required String time,
    required String device,
    required bool isActive,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: isActive ? Colors.green : Colors.grey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColor.hijau1,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  device,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                time,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColor.hijau1,
                ),
              ),
              SizedBox(height: 4),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: isActive ? Colors.green.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  isActive ? "Active" : "Inactive",
                  style: TextStyle(
                    fontSize: 12,
                    color: isActive ? Colors.green : Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAutomationRulesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Automation Rules",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColor.hijau1,
          ),
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 2,
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Icon(
                Icons.auto_awesome,
                color: AppColor.hijau1,
                size: 48,
              ),
              SizedBox(height: 16),
              Text(
                "Smart Automation",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColor.hijau1,
                ),
              ),
              SizedBox(height: 8),
              Text(
                "Set up intelligent automation rules based on sensor readings and environmental conditions.",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // TODO: Navigate to automation setup
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.hijau1,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  "Create Automation Rule",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
