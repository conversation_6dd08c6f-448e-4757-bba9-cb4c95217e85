// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartfarming_bapeltan/common/app_colors.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String username = '';
  String email = '';
  String namaAlat = '';
  bool notificationsEnabled = true;
  bool autoSyncEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    setState(() {
      username = pref.getString('username') ?? 'User';
      email = pref.getString('email') ?? '<EMAIL>';
      namaAlat = pref.getString('namaAlat') ?? 'Smart Farm Device';
      notificationsEnabled = pref.getBool('notifications') ?? true;
      autoSyncEnabled = pref.getBool('autoSync') ?? true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // Profile Header
          _buildProfileHeader(),
          
          SizedBox(height: 24),
          
          // Account Settings
          _buildAccountSettings(),
          
          SizedBox(height: 24),
          
          // App Settings
          _buildAppSettings(),
          
          SizedBox(height: 24),
          
          // Device Settings
          _buildDeviceSettings(),
          
          SizedBox(height: 24),
          
          // Support & About
          _buildSupportSection(),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: AppColor.hijau2,
            child: Text(
              username.isNotEmpty ? username[0].toUpperCase() : 'U',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            username,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColor.hijau1,
            ),
          ),
          SizedBox(height: 4),
          Text(
            email,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // TODO: Navigate to edit profile
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.hijau1,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              "Edit Profile",
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSettings() {
    return _buildSettingsSection(
      title: "Account Settings",
      children: [
        _buildSettingsItem(
          icon: Icons.person,
          title: "Personal Information",
          subtitle: "Update your personal details",
          onTap: () {
            // TODO: Navigate to personal info
          },
        ),
        _buildSettingsItem(
          icon: Icons.security,
          title: "Security",
          subtitle: "Change password and security settings",
          onTap: () {
            // TODO: Navigate to security settings
          },
        ),
        _buildSettingsItem(
          icon: Icons.privacy_tip,
          title: "Privacy",
          subtitle: "Manage your privacy preferences",
          onTap: () {
            // TODO: Navigate to privacy settings
          },
        ),
      ],
    );
  }

  Widget _buildAppSettings() {
    return _buildSettingsSection(
      title: "App Settings",
      children: [
        _buildSwitchItem(
          icon: Icons.notifications,
          title: "Notifications",
          subtitle: "Receive alerts and updates",
          value: notificationsEnabled,
          onChanged: (value) async {
            setState(() {
              notificationsEnabled = value;
            });
            SharedPreferences pref = await SharedPreferences.getInstance();
            await pref.setBool('notifications', value);
          },
        ),
        _buildSwitchItem(
          icon: Icons.sync,
          title: "Auto Sync",
          subtitle: "Automatically sync data",
          value: autoSyncEnabled,
          onChanged: (value) async {
            setState(() {
              autoSyncEnabled = value;
            });
            SharedPreferences pref = await SharedPreferences.getInstance();
            await pref.setBool('autoSync', value);
          },
        ),
        _buildSettingsItem(
          icon: Icons.language,
          title: "Language",
          subtitle: "Choose your preferred language",
          trailing: Text(
            "English",
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          onTap: () {
            // TODO: Navigate to language settings
          },
        ),
      ],
    );
  }

  Widget _buildDeviceSettings() {
    return _buildSettingsSection(
      title: "Device Settings",
      children: [
        _buildSettingsItem(
          icon: Icons.devices,
          title: "Connected Device",
          subtitle: namaAlat,
          onTap: () {
            // TODO: Navigate to device management
          },
        ),
        _buildSettingsItem(
          icon: Icons.wifi,
          title: "Network Settings",
          subtitle: "Configure network connection",
          onTap: () {
            // TODO: Navigate to network settings
          },
        ),
        _buildSettingsItem(
          icon: Icons.update,
          title: "Firmware Update",
          subtitle: "Check for device updates",
          onTap: () {
            // TODO: Navigate to firmware update
          },
        ),
      ],
    );
  }

  Widget _buildSupportSection() {
    return _buildSettingsSection(
      title: "Support & About",
      children: [
        _buildSettingsItem(
          icon: Icons.help,
          title: "Help & Support",
          subtitle: "Get help and contact support",
          onTap: () {
            // TODO: Navigate to help
          },
        ),
        _buildSettingsItem(
          icon: Icons.info,
          title: "About",
          subtitle: "App version and information",
          onTap: () {
            // TODO: Show about dialog
          },
        ),
        _buildSettingsItem(
          icon: Icons.logout,
          title: "Logout",
          subtitle: "Sign out of your account",
          titleColor: AppColor.red,
          onTap: () {
            _showLogoutDialog();
          },
        ),
      ],
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColor.hijau1,
          ),
        ),
        SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 2,
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    Color? titleColor,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColor.hijau3,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: titleColor ?? AppColor.hijau1,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: titleColor ?? AppColor.hijau1,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: trailing ?? Icon(Icons.chevron_right, color: Colors.grey[400]),
      onTap: onTap,
    );
  }

  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColor.hijau3,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColor.hijau1,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColor.hijau1,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColor.hijau1,
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Logout',
            style: TextStyle(
              color: AppColor.hijau1,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement logout functionality
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColor.red,
                foregroundColor: Colors.white,
              ),
              child: Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
